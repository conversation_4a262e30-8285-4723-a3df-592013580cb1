document.addEventListener('DOMContentLoaded', function() {
    // Kontrola existence úkolů a aktualizace stavu
    checkTasksStatus();

    // Kontrola existence profilové fotografie
    checkProfilePhoto();

    // Aktualizace progress baru
    updateProgress();
});

function checkTasksStatus() {
    const taskCards = document.querySelectorAll('.task-card');
    let completedTasks = 0;

    // Funkce pro kontrolu existence souboru pomocí Image objektu
    function checkFileExists(url) {
        return new Promise((resolve) => {
            // Pro HTML soubory použijeme XMLHttpRequest, který funguje lépe s file:// protokolem
            const xhr = new XMLHttpRequest();
            xhr.open('HEAD', url, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    resolve(xhr.status === 200 || xhr.status === 0); // status 0 je OK pro file:// protokol
                }
            };
            xhr.onerror = function() {
                resolve(false);
            };
            try {
                xhr.send();
            } catch (e) {
                resolve(false);
            }
        });
    }

    // Funkce pro načtení obsahu souboru
    function loadFileContent(url) {
        return new Promise((resolve) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 0) {
                        resolve(xhr.responseText);
                    } else {
                        resolve(null);
                    }
                }
            };
            xhr.onerror = function() {
                resolve(null);
            };
            try {
                xhr.send();
            } catch (e) {
                resolve(null);
            }
        });
    }

    // Procházení všech úkolů
    taskCards.forEach(async (card) => {
        const taskNumber = card.dataset.task;
        const taskPath = `../prx${taskNumber}/index.html`;

        const fileExists = await checkFileExists(taskPath);

        if (fileExists) {
            // Úkol existuje - označit jako dokončený
            card.classList.add('completed');
            completedTasks++;

            // Pokus o načtení obsahu pro náhled
            const content = await loadFileContent(taskPath);
            if (content) {
                try {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(content, 'text/html');

                    // Extrakce popisu z různých zdrojů
                    let description = '';

                    // Zkusíme najít popis v meta tagu
                    const metaDesc = doc.querySelector('meta[name="description"]');
                    if (metaDesc) {
                        description = metaDesc.getAttribute('content');
                    }

                    // Pokud není meta popis, zkusíme první odstavec
                    if (!description) {
                        const firstP = doc.querySelector('p, .description');
                        if (firstP) {
                            description = firstP.textContent;
                        }
                    }

                    // Pokud stále není, použijeme title
                    if (!description) {
                        const title = doc.querySelector('title');
                        if (title) {
                            description = title.textContent;
                        }
                    }

                    // Fallback popis
                    if (!description) {
                        description = 'Úkol je dokončen a připraven k zobrazení.';
                    }

                    // Aktualizace náhledu
                    const preview = card.querySelector('.task-preview p');
                    if (preview) {
                        preview.textContent = description.length > 80 ?
                            description.substring(0, 80) + '...' : description;
                    }

                } catch (error) {
                    console.log(`Nelze parsovat obsah úkolu ${taskNumber}:`, error);
                    const preview = card.querySelector('.task-preview p');
                    if (preview) {
                        preview.textContent = 'Úkol je dokončen a připraven k zobrazení.';
                    }
                }
            } else {
                // Soubor existuje, ale nelze načíst obsah
                const preview = card.querySelector('.task-preview p');
                if (preview) {
                    preview.textContent = 'Úkol je dokončen a připraven k zobrazení.';
                }
            }
        } else {
            // Úkol neexistuje
            card.classList.remove('completed');
            const preview = card.querySelector('.task-preview p');
            if (preview) {
                preview.textContent = 'Úkol ještě není dokončen.';
            }
        }

        // Aktualizace počítadla a progress baru po každém úkolu
        updateProgressDisplay();
    });

    function updateProgressDisplay() {
        const completedCards = document.querySelectorAll('.task-card.completed').length;
        document.querySelector('.completed-count').textContent = completedCards;

        const progressFill = document.querySelector('.progress-fill');
        const percentage = (completedCards / 10) * 100;
        progressFill.style.width = percentage + '%';
    }
}

function checkProfilePhoto() {
    const profileImg = document.getElementById('profile-img');
    const placeholder = document.getElementById('photo-placeholder');

    // Pokus o načtení profilové fotografie
    const img = new Image();
    img.onload = function() {
        // Fotografie existuje
        profileImg.style.display = 'block';
        placeholder.style.display = 'none';
    };
    img.onerror = function() {
        // Fotografie neexistuje
        profileImg.style.display = 'none';
        placeholder.style.display = 'flex';
    };
    img.src = 'profile.jpg';
}

function updateProgress() {
    // Animace progress baru při načtení stránky
    setTimeout(() => {
        const progressFill = document.querySelector('.progress-fill');
        progressFill.style.transition = 'width 1s ease-in-out';
    }, 500);
}

// Funkce pro ruční označení úkolu jako dokončeného (pro testování)
function markTaskCompleted(taskNumber) {
    const taskCard = document.querySelector(`[data-task="${taskNumber}"]`);
    if (taskCard) {
        taskCard.classList.add('completed');
        checkTasksStatus();
    }
}

// Funkce pro ruční označení úkolu jako nedokončeného (pro testování)
function markTaskPending(taskNumber) {
    const taskCard = document.querySelector(`[data-task="${taskNumber}"]`);
    if (taskCard) {
        taskCard.classList.remove('completed');
        checkTasksStatus();
    }
}

// Přidání event listenerů pro odkazy
document.querySelectorAll('.task-link').forEach(link => {
    link.addEventListener('click', function(e) {
        const href = this.getAttribute('href');

        // Kontrola, zda soubor existuje před otevřením pomocí XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('HEAD', href, false); // synchronní pro jednodušší handling
        try {
            xhr.send();
            if (xhr.status !== 200 && xhr.status !== 0) {
                e.preventDefault();
                alert('Tento úkol ještě není dokončen nebo soubor neexistuje.');
            }
        } catch (error) {
            e.preventDefault();
            alert('Tento úkol ještě není dokončen nebo soubor neexistuje.');
        }
    });
});

// Funkce pro aktualizaci stavu úkolů (volat při změnách)
function refreshTasksStatus() {
    checkTasksStatus();
}

// Export funkcí pro případné použití v konzoli
window.markTaskCompleted = markTaskCompleted;
window.markTaskPending = markTaskPending;
window.refreshTasksStatus = refreshTasksStatus;
