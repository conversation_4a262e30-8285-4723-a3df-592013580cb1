document.addEventListener('DOMContentLoaded', function() {
    // Inicializace ikon pro všechny úkoly (výchozí stav - nedokončené)
    initializeTaskIcons();

    // Kontrola existence úkolů a aktualizace stavu
    checkTasksStatus();

    // Kontrola existence profilové fotografie
    checkProfilePhoto();

    // Aktualizace progress baru
    updateProgress();
});

// Funkce pro vytvoření ikony podle stavu úkolu
function createTaskIcon(isCompleted) {
    const icon = document.createElement('i');
    icon.className = isCompleted
        ? 'fas fa-circle-check completed-icon'
        : 'fas fa-circle-xmark pending-icon';
    return icon;
}

// Funkce pro aktualizaci ikony úkolu
function updateTaskIcon(taskCard, isCompleted) {
    const statusIndicator = taskCard.querySelector('.status-indicator');

    // Vymaž všechny existující ikony
    statusIndicator.innerHTML = '';

    // Vytvoř a přidej novou ikonu
    const icon = createTaskIcon(isCompleted);
    statusIndicator.appendChild(icon);

    // Aktualizuj CSS třídu pro styling
    if (isCompleted) {
        taskCard.classList.add('completed');
    } else {
        taskCard.classList.remove('completed');
    }
}

// Funkce pro inicializaci všech ikon (výchozí stav)
function initializeTaskIcons() {
    const taskCards = document.querySelectorAll('.task-card');
    taskCards.forEach(card => {
        updateTaskIcon(card, false); // Výchozí stav - nedokončené
    });
}

async function checkTasksStatus() {
    const taskCards = document.querySelectorAll('.task-card');
    let completedTasks = 0;

    for (let card of taskCards) {
        const taskNumber = card.dataset.task;
        const taskPath = `../prx${taskNumber}/index.html`;

        try {
            // Pokus o načtení úkolu
            const response = await fetch(taskPath, { method: 'HEAD' });

            if (response.ok) {
                // Úkol existuje - označit jako dokončený
                updateTaskIcon(card, true);
                completedTasks++;

                // Pokus o načtení obsahu pro náhled
                try {
                    const contentResponse = await fetch(taskPath);
                    const content = await contentResponse.text();
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(content, 'text/html');

                    // Extrakce titulu nebo popisu
                    const title = doc.querySelector('title')?.textContent ||
                                 doc.querySelector('h1')?.textContent ||
                                 `Úkol ${taskNumber}`;

                    const description = doc.querySelector('meta[name="description"]')?.content ||
                                      doc.querySelector('p')?.textContent?.substring(0, 100) ||
                                      'Úkol je dokončen a připraven k zobrazení.';

                    // Aktualizace náhledu
                    const preview = card.querySelector('.task-preview p');
                    if (preview) {
                        preview.textContent = description.length > 80 ?
                            description.substring(0, 80) + '...' : description;
                    }

                } catch (error) {
                    console.log(`Nelze načíst obsah úkolu ${taskNumber}:`, error);
                }
            } else {
                // Úkol neexistuje
                updateTaskIcon(card, false);
                const preview = card.querySelector('.task-preview p');
                if (preview) {
                    preview.textContent = 'Úkol ještě není dokončen.';
                }
            }
        } catch (error) {
            // Chyba při načítání - úkol pravděpodobně neexistuje
            updateTaskIcon(card, false);
            const preview = card.querySelector('.task-preview p');
            if (preview) {
                preview.textContent = 'Úkol ještě není dokončen.';
            }
        }
    }

    // Aktualizace počítadla
    document.querySelector('.completed-count').textContent = completedTasks;

    // Aktualizace progress baru
    const progressFill = document.querySelector('.progress-fill');
    const percentage = (completedTasks / 10) * 100;
    progressFill.style.width = percentage + '%';
}

function checkProfilePhoto() {
    const profileImg = document.getElementById('profile-img');
    const placeholder = document.getElementById('photo-placeholder');

    // Pokus o načtení profilové fotografie
    const img = new Image();
    img.onload = function() {
        // Fotografie existuje
        profileImg.style.display = 'block';
        placeholder.style.display = 'none';
    };
    img.onerror = function() {
        // Fotografie neexistuje
        profileImg.style.display = 'none';
        placeholder.style.display = 'flex';
    };
    img.src = 'profile.jpg';
}

function updateProgress() {
    // Animace progress baru při načtení stránky
    setTimeout(() => {
        const progressFill = document.querySelector('.progress-fill');
        progressFill.style.transition = 'width 1s ease-in-out';
    }, 500);
}

// Funkce pro ruční označení úkolu jako dokončeného (pro testování)
function markTaskCompleted(taskNumber) {
    const taskCard = document.querySelector(`[data-task="${taskNumber}"]`);
    if (taskCard) {
        updateTaskIcon(taskCard, true);
        checkTasksStatus();
    }
}

// Funkce pro ruční označení úkolu jako nedokončeného (pro testování)
function markTaskPending(taskNumber) {
    const taskCard = document.querySelector(`[data-task="${taskNumber}"]`);
    if (taskCard) {
        updateTaskIcon(taskCard, false);
        checkTasksStatus();
    }
}

// Přidání event listenerů pro odkazy
document.querySelectorAll('.task-link').forEach(link => {
    link.addEventListener('click', function(e) {
        const href = this.getAttribute('href');

        // Kontrola, zda soubor existuje před otevřením
        fetch(href, { method: 'HEAD' })
            .then(response => {
                if (!response.ok) {
                    e.preventDefault();
                    alert('Tento úkol ještě není dokončen nebo soubor neexistuje.');
                }
            })
            .catch(error => {
                e.preventDefault();
                alert('Tento úkol ještě není dokončen nebo soubor neexistuje.');
            });
    });
});

// Funkce pro aktualizaci stavu úkolů (volat při změnách)
function refreshTasksStatus() {
    checkTasksStatus();
}

// Export funkcí pro případné použití v konzoli
window.markTaskCompleted = markTaskCompleted;
window.markTaskPending = markTaskPending;
window.refreshTasksStatus = refreshTasksStatus;
