<?php
header("Content-Type: application/json");

$savesDir = __DIR__ . "/saves";

if (!file_exists($savesDir)) {
    mkdir($savesDir);
}

$action = $_GET["action"] ?? "";

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $input = json_decode(file_get_contents("php://input"), true);
    if ($input["action"] === "save") {
        $name = preg_replace('/[^a-zA-Z0-9_-]/', '', $input["name"]);
        file_put_contents("$savesDir/$name.json", json_encode($input["data"], JSON_PRETTY_PRINT));
        echo json_encode(["status" => "saved"]);
    }
    exit;
}

if ($action === "load" && isset($_GET["name"])) {
    $name = preg_replace('/[^a-zA-Z0-9_-]/', '', $_GET["name"]);
    $path = "$savesDir/$name.json";
    if (file_exists($path)) {
        echo file_get_contents($path);
    } else {
        echo json_encode([]);
    }
    exit;
}

if ($action === "list") {
    $files = array_filter(scandir($savesDir), fn($f) => str_ends_with($f, ".json"));
    $names = array_map(fn($f) => basename($f, ".json"), $files);
    echo json_encode(array_values($names));
    exit;
}
?>