class RelaxationApp {
    constructor() {
        this.mouseX = 0;
        this.mouseY = 0;
        this.currentSeason = 'spring';
        this.particles = [];
        this.audioContext = null;
        this.audioSources = {};
        this.isStarted = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAudio();
        this.createCursorTrail();
        this.startParticleSystem();
    }

    setupEventListeners() {
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.getElementById('start-button').addEventListener('click', () => this.startExperience());

        // Interactive objects
        document.getElementById('windmill').addEventListener('mouseenter', () => this.activateWindmill());
        document.getElementById('windmill').addEventListener('mouseleave', () => this.deactivateWindmill());

        document.getElementById('cat').addEventListener('mouseenter', () => this.activateCat());
        document.getElementById('cat').addEventListener('mouseleave', () => this.deactivateCat());

        document.getElementById('cloud').addEventListener('mouseenter', () => this.activateCloud());
        document.getElementById('cloud').addEventListener('mouseleave', () => this.deactivateCloud());
    }

    handleMouseMove(e) {
        this.mouseX = e.clientX;
        this.mouseY = e.clientY;

        // Update cursor position
        const cursor = document.getElementById('cursor');
        cursor.style.left = this.mouseX - 10 + 'px';
        cursor.style.top = this.mouseY - 10 + 'px';

        // Determine season based on mouse position
        this.updateSeason();

        // Update overlays
        this.updateOverlays();

        // Create cursor trail
        this.createCursorTrail();
    }

    updateSeason() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        let newSeason;
        if (this.mouseX < width / 2 && this.mouseY < height / 2) {
            newSeason = 'spring';
        } else if (this.mouseX >= width / 2 && this.mouseY < height / 2) {
            newSeason = 'summer';
        } else if (this.mouseX < width / 2 && this.mouseY >= height / 2) {
            newSeason = 'autumn';
        } else {
            newSeason = 'winter';
        }

        if (newSeason !== this.currentSeason) {
            this.currentSeason = newSeason;
            this.updateAudio();
            this.updateCursorStyle();
        }
    }

    updateOverlays() {
        const overlays = document.querySelectorAll('.season-overlay');
        overlays.forEach(overlay => overlay.style.opacity = '0');

        const currentOverlay = document.getElementById(`${this.currentSeason}-overlay`);
        if (currentOverlay) {
            currentOverlay.style.opacity = '1';
            currentOverlay.style.setProperty('--mouse-x', (this.mouseX / window.innerWidth * 100) + '%');
            currentOverlay.style.setProperty('--mouse-y', (this.mouseY / window.innerHeight * 100) + '%');
        }
    }

    updateCursorStyle() {
        const cursor = document.getElementById('cursor');
        const colors = {
            spring: 'radial-gradient(circle, rgba(144,238,144,0.8) 0%, transparent 70%)',
            summer: 'radial-gradient(circle, rgba(255,215,0,0.8) 0%, transparent 70%)',
            autumn: 'radial-gradient(circle, rgba(255,140,0,0.8) 0%, transparent 70%)',
            winter: 'radial-gradient(circle, rgba(173,216,230,0.8) 0%, transparent 70%)'
        };
        cursor.style.background = colors[this.currentSeason];
    }

    createCursorTrail() {
        const trail = document.createElement('div');
        trail.className = 'cursor-trail';
        trail.style.left = this.mouseX - 5 + 'px';
        trail.style.top = this.mouseY - 5 + 'px';

        const colors = {
            spring: 'rgba(144,238,144,0.6)',
            summer: 'rgba(255,215,0,0.6)',
            autumn: 'rgba(255,140,0,0.6)',
            winter: 'rgba(173,216,230,0.6)'
        };
        trail.style.background = colors[this.currentSeason];

        document.body.appendChild(trail);

        setTimeout(() => {
            if (trail.parentNode) {
                trail.parentNode.removeChild(trail);
            }
        }, 1000);
    }

    startParticleSystem() {
        setInterval(() => {
            if (this.isStarted) {
                this.createParticle();
            }
        }, 100);

        setInterval(() => {
            this.cleanupParticles();
        }, 1000);
    }

    createParticle() {
        const container = document.getElementById('particles-container');
        const particle = document.createElement('div');
        particle.className = 'particle';

        const types = {
            spring: 'raindrop',
            summer: 'sand',
            autumn: 'leaf',
            winter: 'snowflake'
        };

        particle.classList.add(types[this.currentSeason]);

        // Random position across screen width
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
        particle.style.animationDelay = Math.random() * 0.5 + 's';

        container.appendChild(particle);
        this.particles.push(particle);
    }

    cleanupParticles() {
        this.particles = this.particles.filter(particle => {
            if (particle.offsetTop > window.innerHeight) {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
                return false;
            }
            return true;
        });
    }

    setupAudio() {
        // Initialize Web Audio API
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.audioSources = {};
        this.gainNodes = {};

        // Create audio sources for each season
        this.createSeasonAudio();
    }

    createSeasonAudio() {
        const seasons = ['spring', 'summer', 'autumn', 'winter'];

        seasons.forEach(season => {
            this.gainNodes[season] = this.audioContext.createGain();
            this.gainNodes[season].gain.value = 0;
            this.gainNodes[season].connect(this.audioContext.destination);

            // Create different ambient sounds for each season
            this.audioSources[season] = this.createAmbientSound(season);
        });

        // Create interactive object sounds
        this.createInteractiveAudio();
    }

    createAmbientSound(season) {
        const oscillator = this.audioContext.createOscillator();
        const filter = this.audioContext.createBiquadFilter();
        const noise = this.createWhiteNoise();

        switch(season) {
            case 'spring':
                // Rain-like sound
                filter.type = 'lowpass';
                filter.frequency.value = 800;
                noise.connect(filter);
                filter.connect(this.gainNodes[season]);
                break;
            case 'summer':
                // Desert wind
                oscillator.frequency.value = 200;
                oscillator.type = 'sawtooth';
                filter.type = 'highpass';
                filter.frequency.value = 300;
                oscillator.connect(filter);
                filter.connect(this.gainNodes[season]);
                oscillator.start();
                break;
            case 'autumn':
                // Rustling leaves
                filter.type = 'bandpass';
                filter.frequency.value = 400;
                noise.connect(filter);
                filter.connect(this.gainNodes[season]);
                break;
            case 'winter':
                // Wind and snow
                oscillator.frequency.value = 150;
                oscillator.type = 'sine';
                filter.type = 'lowpass';
                filter.frequency.value = 500;
                oscillator.connect(filter);
                filter.connect(this.gainNodes[season]);
                oscillator.start();
                break;
        }

        return { oscillator, filter, noise };
    }

    createWhiteNoise() {
        const bufferSize = 2 * this.audioContext.sampleRate;
        const noiseBuffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = noiseBuffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = Math.random() * 2 - 1;
        }

        const whiteNoise = this.audioContext.createBufferSource();
        whiteNoise.buffer = noiseBuffer;
        whiteNoise.loop = true;
        whiteNoise.start(0);

        return whiteNoise;
    }

    createInteractiveAudio() {
        // Create simple tones for interactive objects
        this.gainNodes.windmill = this.audioContext.createGain();
        this.gainNodes.windmill.gain.value = 0;
        this.gainNodes.windmill.connect(this.audioContext.destination);

        this.gainNodes.cat = this.audioContext.createGain();
        this.gainNodes.cat.gain.value = 0;
        this.gainNodes.cat.connect(this.audioContext.destination);

        this.gainNodes.cloud = this.audioContext.createGain();
        this.gainNodes.cloud.gain.value = 0;
        this.gainNodes.cloud.connect(this.audioContext.destination);
    }

    startExperience() {
        this.isStarted = true;
        document.getElementById('start-button').style.display = 'none';

        // Resume audio context if suspended
        if (this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }

        // Start ambient audio
        this.updateAudio();
    }

    updateAudio() {
        if (!this.isStarted || !this.audioContext) return;

        // Fade out all season audio
        Object.keys(this.gainNodes).forEach(key => {
            if (['spring', 'summer', 'autumn', 'winter'].includes(key)) {
                this.fadeGain(this.gainNodes[key], 0, 1000);
            }
        });

        // Fade in current season audio
        const currentGain = this.gainNodes[this.currentSeason];
        if (currentGain) {
            this.fadeGain(currentGain, 0.1, 1000);
        }
    }

    fadeGain(gainNode, targetVolume, duration) {
        if (!gainNode) return;

        const startVolume = gainNode.gain.value;
        const volumeChange = targetVolume - startVolume;
        const steps = 20;
        const stepTime = duration / steps;
        const stepVolume = volumeChange / steps;

        let currentStep = 0;
        const fadeInterval = setInterval(() => {
            currentStep++;
            const newVolume = Math.max(0, Math.min(1, startVolume + (stepVolume * currentStep)));
            gainNode.gain.setValueAtTime(newVolume, this.audioContext.currentTime);

            if (currentStep >= steps) {
                clearInterval(fadeInterval);
            }
        }, stepTime);
    }

    // Interactive object methods
    activateWindmill() {
        const windmill = document.getElementById('windmill');
        windmill.classList.add('rotating');

        if (this.isStarted && this.gainNodes.windmill) {
            // Create windmill sound
            const oscillator = this.audioContext.createOscillator();
            oscillator.frequency.value = 100;
            oscillator.type = 'sawtooth';
            oscillator.connect(this.gainNodes.windmill);
            oscillator.start();
            this.windmillOscillator = oscillator;

            this.fadeGain(this.gainNodes.windmill, 0.05, 500);
        }
    }

    deactivateWindmill() {
        const windmill = document.getElementById('windmill');
        windmill.classList.remove('rotating');

        if (this.gainNodes.windmill) {
            this.fadeGain(this.gainNodes.windmill, 0, 500);

            if (this.windmillOscillator) {
                setTimeout(() => {
                    this.windmillOscillator.stop();
                    this.windmillOscillator = null;
                }, 500);
            }
        }
    }

    activateCat() {
        const cat = document.getElementById('cat');
        cat.classList.add('cat-active');

        if (this.isStarted && this.gainNodes.cat) {
            // Create purring sound
            const oscillator = this.audioContext.createOscillator();
            oscillator.frequency.value = 50;
            oscillator.type = 'triangle';

            const lfo = this.audioContext.createOscillator();
            lfo.frequency.value = 5;
            const lfoGain = this.audioContext.createGain();
            lfoGain.gain.value = 10;

            lfo.connect(lfoGain);
            lfoGain.connect(oscillator.frequency);

            oscillator.connect(this.gainNodes.cat);
            oscillator.start();
            lfo.start();

            this.catOscillator = oscillator;
            this.catLfo = lfo;

            this.fadeGain(this.gainNodes.cat, 0.03, 500);
        }
    }

    deactivateCat() {
        const cat = document.getElementById('cat');
        cat.classList.remove('cat-active');

        if (this.gainNodes.cat) {
            this.fadeGain(this.gainNodes.cat, 0, 500);

            if (this.catOscillator && this.catLfo) {
                setTimeout(() => {
                    this.catOscillator.stop();
                    this.catLfo.stop();
                    this.catOscillator = null;
                    this.catLfo = null;
                }, 500);
            }
        }
    }

    activateCloud() {
        const cloud = document.getElementById('cloud');
        cloud.classList.add('moving-cloud');

        if (this.isStarted && this.gainNodes.cloud) {
            // Create thunder/wind sound
            const noise = this.createWhiteNoise();
            const filter = this.audioContext.createBiquadFilter();
            filter.type = 'lowpass';
            filter.frequency.value = 200;

            noise.connect(filter);
            filter.connect(this.gainNodes.cloud);

            this.cloudNoise = noise;
            this.cloudFilter = filter;

            this.fadeGain(this.gainNodes.cloud, 0.02, 500);
        }
    }

    deactivateCloud() {
        const cloud = document.getElementById('cloud');
        cloud.classList.remove('moving-cloud');

        if (this.gainNodes.cloud) {
            this.fadeGain(this.gainNodes.cloud, 0, 500);

            if (this.cloudNoise) {
                setTimeout(() => {
                    this.cloudNoise.stop();
                    this.cloudNoise = null;
                    this.cloudFilter = null;
                }, 500);
            }
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RelaxationApp();
});
