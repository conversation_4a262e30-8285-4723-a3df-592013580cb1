class RelaxationApp {
    constructor() {
        this.mouseX = 0;
        this.mouseY = 0;
        this.currentSeason = 'spring';
        this.particles = [];
        this.audioContext = null;
        this.audioSources = {};
        this.isStarted = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAudio();
        this.createCursorTrail();
        this.startParticleSystem();
    }

    setupEventListeners() {
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.getElementById('start-button').addEventListener('click', () => this.startExperience());

        // Interactive objects
        document.getElementById('windmill').addEventListener('mouseenter', () => this.activateWindmill());
        document.getElementById('windmill').addEventListener('mouseleave', () => this.deactivateWindmill());

        document.getElementById('cat').addEventListener('mouseenter', () => this.activateCat());
        document.getElementById('cat').addEventListener('mouseleave', () => this.deactivateCat());

        document.getElementById('cloud').addEventListener('mouseenter', () => this.activateCloud());
        document.getElementById('cloud').addEventListener('mouseleave', () => this.deactivateCloud());
    }

    handleMouseMove(e) {
        this.mouseX = e.clientX;
        this.mouseY = e.clientY;

        // Update cursor position
        const cursor = document.getElementById('cursor');
        cursor.style.left = this.mouseX - 10 + 'px';
        cursor.style.top = this.mouseY - 10 + 'px';

        // Determine season based on mouse position
        this.updateSeason();

        // Update overlays
        this.updateOverlays();

        // Create cursor trail
        this.createCursorTrail();
    }

    updateSeason() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        let newSeason;
        if (this.mouseX < width / 2 && this.mouseY < height / 2) {
            newSeason = 'spring';
        } else if (this.mouseX >= width / 2 && this.mouseY < height / 2) {
            newSeason = 'summer';
        } else if (this.mouseX < width / 2 && this.mouseY >= height / 2) {
            newSeason = 'autumn';
        } else {
            newSeason = 'winter';
        }

        if (newSeason !== this.currentSeason) {
            this.currentSeason = newSeason;
            this.updateAudio();
            this.updateCursorStyle();
        }
    }

    updateOverlays() {
        const overlays = document.querySelectorAll('.season-overlay');
        overlays.forEach(overlay => overlay.style.opacity = '0');

        const currentOverlay = document.getElementById(`${this.currentSeason}-overlay`);
        if (currentOverlay) {
            currentOverlay.style.opacity = '1';
            currentOverlay.style.setProperty('--mouse-x', (this.mouseX / window.innerWidth * 100) + '%');
            currentOverlay.style.setProperty('--mouse-y', (this.mouseY / window.innerHeight * 100) + '%');
        }
    }

    updateCursorStyle() {
        const cursor = document.getElementById('cursor');
        const colors = {
            spring: 'radial-gradient(circle, rgba(144,238,144,0.8) 0%, transparent 70%)',
            summer: 'radial-gradient(circle, rgba(255,215,0,0.8) 0%, transparent 70%)',
            autumn: 'radial-gradient(circle, rgba(255,140,0,0.8) 0%, transparent 70%)',
            winter: 'radial-gradient(circle, rgba(173,216,230,0.8) 0%, transparent 70%)'
        };
        cursor.style.background = colors[this.currentSeason];
    }

    createCursorTrail() {
        const trail = document.createElement('div');
        trail.className = 'cursor-trail';
        trail.style.left = this.mouseX - 5 + 'px';
        trail.style.top = this.mouseY - 5 + 'px';

        const colors = {
            spring: 'rgba(144,238,144,0.6)',
            summer: 'rgba(255,215,0,0.6)',
            autumn: 'rgba(255,140,0,0.6)',
            winter: 'rgba(173,216,230,0.6)'
        };
        trail.style.background = colors[this.currentSeason];

        document.body.appendChild(trail);

        setTimeout(() => {
            if (trail.parentNode) {
                trail.parentNode.removeChild(trail);
            }
        }, 1000);
    }

    startParticleSystem() {
        setInterval(() => {
            if (this.isStarted) {
                this.createParticle();
            }
        }, 300); // Slower particle generation

        setInterval(() => {
            this.cleanupParticles();
        }, 2000);
    }

    createParticle() {
        const container = document.getElementById('particles-container');
        const particle = document.createElement('div');
        particle.className = 'particle';

        const types = {
            spring: 'raindrop',
            summer: 'sand',
            autumn: 'leaf',
            winter: 'snowflake'
        };

        particle.classList.add(types[this.currentSeason]);

        // Random position across screen width
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.animationDuration = (Math.random() * 8 + 6) + 's'; // Much slower fall
        particle.style.animationDelay = Math.random() * 2 + 's';

        // Add gentle swaying motion
        particle.style.transform = `translateX(${Math.sin(Date.now() * 0.001) * 20}px)`;

        container.appendChild(particle);
        this.particles.push(particle);
    }

    cleanupParticles() {
        this.particles = this.particles.filter(particle => {
            if (particle.offsetTop > window.innerHeight) {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
                return false;
            }
            return true;
        });
    }

    setupAudio() {
        // Create HTML5 audio elements for natural sounds
        this.audioSources = {
            spring: this.createNaturalAudio('spring'),
            summer: this.createNaturalAudio('summer'),
            autumn: this.createNaturalAudio('autumn'),
            winter: this.createNaturalAudio('winter'),
            windmill: this.createNaturalAudio('windmill'),
            cat: this.createNaturalAudio('cat'),
            cloud: this.createNaturalAudio('cloud')
        };
    }

    createNaturalAudio(type) {
        const audio = new Audio();
        audio.loop = true;
        audio.volume = 0;
        audio.preload = 'auto';

        // Use data URLs for simple natural-sounding audio
        switch(type) {
            case 'spring':
                // Gentle rain sound using pink noise
                audio.src = this.generateRainSound();
                break;
            case 'summer':
                // Gentle breeze and birds
                audio.src = this.generateSummerSound();
                break;
            case 'autumn':
                // Soft wind through leaves
                audio.src = this.generateAutumnSound();
                break;
            case 'winter':
                // Gentle wind
                audio.src = this.generateWinterSound();
                break;
            case 'windmill':
                // Soft whooshing
                audio.src = this.generateWindmillSound();
                break;
            case 'cat':
                // Gentle purring
                audio.src = this.generatePurrSound();
                break;
            case 'cloud':
                // Distant thunder
                audio.src = this.generateThunderSound();
                break;
        }

        return audio;
    }

    generateRainSound() {
        // Create gentle rain sound using Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 10; // 10 seconds loop
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        // Generate pink noise for rain
        let b0 = 0, b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;
        for (let i = 0; i < data.length; i++) {
            const white = Math.random() * 2 - 1;
            b0 = 0.99886 * b0 + white * 0.0555179;
            b1 = 0.99332 * b1 + white * 0.0750759;
            b2 = 0.96900 * b2 + white * 0.1538520;
            b3 = 0.86650 * b3 + white * 0.3104856;
            b4 = 0.55000 * b4 + white * 0.5329522;
            b5 = -0.7616 * b5 - white * 0.0168980;
            const pink = b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362;
            b6 = white * 0.115926;
            data[i] = pink * 0.1; // Very gentle volume
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generateSummerSound() {
        // Gentle breeze with subtle bird chirps
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 15;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Gentle wind base
            let sample = Math.sin(t * 0.5) * 0.02;
            // Add very subtle high frequency content for leaves
            sample += Math.sin(t * 3 + Math.sin(t * 0.3)) * 0.01;
            // Occasional bird chirp (very subtle)
            if (Math.random() < 0.0001) {
                sample += Math.sin(t * 1000) * 0.005 * Math.exp(-((i % 1000) / 100));
            }
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generateAutumnSound() {
        // Soft rustling leaves
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 12;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Gentle rustling using filtered noise
            let sample = (Math.random() * 2 - 1) * 0.03;
            // Apply gentle filtering
            sample *= Math.sin(t * 2 + Math.sin(t * 0.1)) * 0.5 + 0.5;
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generateWinterSound() {
        // Gentle winter wind
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 20;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Gentle wind sound
            let sample = Math.sin(t * 0.3) * 0.02;
            sample += Math.sin(t * 0.7 + Math.sin(t * 0.1)) * 0.01;
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generateWindmillSound() {
        // Soft whooshing
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 8;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Gentle whoosh pattern
            let sample = Math.sin(t * 2 * Math.PI) * 0.02;
            sample *= Math.sin(t * 0.5) * 0.5 + 0.5;
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generatePurrSound() {
        // Gentle cat purring
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 6;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Low frequency purr with gentle modulation
            let sample = Math.sin(t * 50 * 2 * Math.PI) * 0.015;
            sample *= Math.sin(t * 5 * 2 * Math.PI) * 0.3 + 0.7;
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    generateThunderSound() {
        // Very distant, gentle thunder
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const duration = 25;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            // Very gentle rumble
            let sample = Math.sin(t * 30 * 2 * Math.PI) * 0.005;
            sample += Math.sin(t * 15 * 2 * Math.PI) * 0.003;
            // Occasional very gentle rumble
            if (t > 5 && t < 8) {
                sample += (Math.random() * 2 - 1) * 0.01 * Math.exp(-(t - 6.5) * 2);
            }
            data[i] = sample;
        }

        return this.bufferToDataURL(buffer, audioContext);
    }

    bufferToDataURL(buffer, audioContext) {
        // Convert audio buffer to data URL for use with HTML5 audio
        const length = buffer.length;
        const arrayBuffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(arrayBuffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, audioContext.sampleRate, true);
        view.setUint32(28, audioContext.sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // Convert float samples to 16-bit PCM
        const data = buffer.getChannelData(0);
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, data[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
        }

        const blob = new Blob([arrayBuffer], { type: 'audio/wav' });
        return URL.createObjectURL(blob);
    }

    startExperience() {
        this.isStarted = true;
        document.getElementById('start-button').style.display = 'none';

        // Start ambient audio
        this.updateAudio();
    }

    updateAudio() {
        if (!this.isStarted) return;

        // Fade out all season audio
        Object.keys(this.audioSources).forEach(key => {
            if (['spring', 'summer', 'autumn', 'winter'].includes(key)) {
                this.fadeAudio(this.audioSources[key], 0, 2000);
            }
        });

        // Fade in current season audio
        const currentAudio = this.audioSources[this.currentSeason];
        if (currentAudio) {
            currentAudio.play().catch(() => {});
            this.fadeAudio(currentAudio, 0.15, 2000); // Very gentle volume
        }
    }

    fadeAudio(audio, targetVolume, duration) {
        if (!audio) return;

        const startVolume = audio.volume;
        const volumeChange = targetVolume - startVolume;
        const steps = 40; // More steps for smoother transition
        const stepTime = duration / steps;
        const stepVolume = volumeChange / steps;

        let currentStep = 0;
        const fadeInterval = setInterval(() => {
            currentStep++;
            const newVolume = Math.max(0, Math.min(1, startVolume + (stepVolume * currentStep)));
            audio.volume = newVolume;

            if (currentStep >= steps) {
                clearInterval(fadeInterval);
                if (targetVolume === 0) {
                    audio.pause();
                }
            }
        }, stepTime);
    }

    // Interactive object methods
    activateWindmill() {
        const windmill = document.getElementById('windmill');
        windmill.classList.add('rotating');

        if (this.isStarted && this.audioSources.windmill) {
            this.audioSources.windmill.play().catch(() => {});
            this.fadeAudio(this.audioSources.windmill, 0.08, 1000);
        }
    }

    deactivateWindmill() {
        const windmill = document.getElementById('windmill');
        windmill.classList.remove('rotating');

        if (this.audioSources.windmill) {
            this.fadeAudio(this.audioSources.windmill, 0, 1000);
        }
    }

    activateCat() {
        const cat = document.getElementById('cat');
        cat.classList.add('cat-active');

        if (this.isStarted && this.audioSources.cat) {
            this.audioSources.cat.play().catch(() => {});
            this.fadeAudio(this.audioSources.cat, 0.06, 1000);
        }
    }

    deactivateCat() {
        const cat = document.getElementById('cat');
        cat.classList.remove('cat-active');

        if (this.audioSources.cat) {
            this.fadeAudio(this.audioSources.cat, 0, 1000);
        }
    }

    activateCloud() {
        const cloud = document.getElementById('cloud');
        cloud.classList.add('moving-cloud');

        if (this.isStarted && this.audioSources.cloud) {
            this.audioSources.cloud.play().catch(() => {});
            this.fadeAudio(this.audioSources.cloud, 0.04, 1000);
        }
    }

    deactivateCloud() {
        const cloud = document.getElementById('cloud');
        cloud.classList.remove('moving-cloud');

        if (this.audioSources.cloud) {
            this.fadeAudio(this.audioSources.cloud, 0, 1000);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RelaxationApp();
});
