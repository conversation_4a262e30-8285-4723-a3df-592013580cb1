/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styly */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.author-info {
    display: flex;
    align-items: center;
    gap: 30px;
}

.author-photo {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #667eea;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.author-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    text-align: center;
}

.photo-placeholder i {
    font-size: 40px;
    margin-bottom: 8px;
}

.author-details h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 1.2rem;
    color: #718096;
    margin-bottom: 20px;
}

.progress-summary {
    font-size: 1.1rem;
    color: #4a5568;
    font-weight: 500;
}

.progress-bar {
    width: 300px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    margin-top: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* Grid úkolů */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.task-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.task-card:hover::before {
    opacity: 1;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.task-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.status-indicator {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.status-indicator i {
    font-size: 1.5rem;
}

.status-indicator .completed-icon {
    color: #48bb78;
}

.status-indicator .pending-icon {
    color: #ed8936;
}

.task-card.completed {
    border-left: 4px solid #48bb78;
}

.task-preview {
    margin-bottom: 20px;
    color: #718096;
    font-size: 0.95rem;
    line-height: 1.5;
}

.task-actions {
    margin-top: auto;
}

.task-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.task-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.task-link i {
    font-size: 0.8rem;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responzivní design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .author-photo {
        width: 100px;
        height: 100px;
    }

    .author-details h1 {
        font-size: 2rem;
    }

    .progress-bar {
        width: 100%;
        max-width: 300px;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .header {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .task-card {
        padding: 20px;
    }

    .author-details h1 {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }
}
