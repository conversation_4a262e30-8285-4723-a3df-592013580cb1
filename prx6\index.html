<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relaxační web - Čtyři roční období</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            cursor: none;
        }

        body {
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            background: #000;
            height: 100vh;
            width: 100vw;
        }

        #background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg,
                    #90EE90 0%, #90EE90 25%,
                    #FFD700 25%, #FFD700 50%,
                    #FF8C00 50%, #FF8C00 75%,
                    #ADD8E6 75%, #ADD8E6 100%
                );
            transition: filter 0.5s ease;
        }

        #background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(34,139,34,0.8) 0%, transparent 20%),
                radial-gradient(circle at 75% 25%, rgba(255,140,0,0.8) 0%, transparent 20%),
                radial-gradient(circle at 25% 75%, rgba(255,69,0,0.8) 0%, transparent 20%),
                radial-gradient(circle at 75% 75%, rgba(70,130,180,0.8) 0%, transparent 20%);
        }

        #particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            pointer-events: none;
            animation: fall linear infinite;
        }

        .leaf {
            width: 6px;
            height: 6px;
            background: linear-gradient(45deg, #ff8c42, #ff6b35);
            border-radius: 50% 0;
            transform: rotate(45deg);
            opacity: 0.7;
        }

        .snowflake {
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            box-shadow: 0 0 4px rgba(255,255,255,0.5);
        }

        .raindrop {
            width: 1px;
            height: 12px;
            background: linear-gradient(to bottom, transparent, rgba(74,144,226,0.6));
            border-radius: 0 0 50% 50%;
        }

        .sand {
            width: 2px;
            height: 2px;
            background: rgba(222,184,135,0.6);
            border-radius: 50%;
        }

        @keyframes fall {
            from {
                transform: translateY(-20px);
                opacity: 1;
            }
            to {
                transform: translateY(100vh);
                opacity: 0.3;
            }
        }

        #cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
            pointer-events: none;
            z-index: 1000;
            transition: all 0.1s ease;
        }

        .cursor-trail {
            position: fixed;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
            animation: fadeOut 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 0.8;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.3);
            }
        }

        #start-button {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 40px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 10px;
            font-size: 18px;
            cursor: pointer;
            z-index: 2000;
            transition: all 0.3s ease;
        }

        #start-button:hover {
            background: rgba(255,255,255,1);
            transform: translate(-50%, -50%) scale(1.05);
        }

        .season-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .spring-overlay {
            background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(144, 238, 144, 0.3) 0%,
                rgba(144, 238, 144, 0.1) 30%,
                transparent 60%);
        }

        .summer-overlay {
            background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(255, 215, 0, 0.3) 0%,
                rgba(255, 215, 0, 0.1) 30%,
                transparent 60%);
        }

        .autumn-overlay {
            background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(255, 140, 0, 0.3) 0%,
                rgba(255, 140, 0, 0.1) 30%,
                transparent 60%);
        }

        .winter-overlay {
            background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(173, 216, 230, 0.3) 0%,
                rgba(173, 216, 230, 0.1) 30%,
                transparent 60%);
        }

        .interactive-object {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #windmill {
            top: 20%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="5" fill="%23654321"/><path d="M50 50 L50 20 L45 25 Z" fill="%23228B22"/><path d="M50 50 L80 50 L75 45 Z" fill="%23228B22"/><path d="M50 50 L50 80 L55 75 Z" fill="%23228B22"/><path d="M50 50 L20 50 L25 55 Z" fill="%23228B22"/></svg>') no-repeat center;
            background-size: contain;
        }

        #cat {
            bottom: 30%;
            left: 20%;
            width: 50px;
            height: 40px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 80"><ellipse cx="50" cy="60" rx="30" ry="15" fill="%23FF6347"/><circle cx="40" cy="45" r="3" fill="%23000"/><circle cx="60" cy="45" r="3" fill="%23000"/><path d="M35 35 L40 25 L45 35 Z" fill="%23FF6347"/><path d="M55 35 L60 25 L65 35 Z" fill="%23FF6347"/></svg>') no-repeat center;
            background-size: contain;
        }

        #cloud {
            top: 15%;
            left: 30%;
            width: 80px;
            height: 40px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 50"><ellipse cx="25" cy="35" rx="20" ry="15" fill="%23E6E6FA"/><ellipse cx="50" cy="25" rx="25" ry="20" fill="%23E6E6FA"/><ellipse cx="75" cy="35" rx="20" ry="15" fill="%23E6E6FA"/></svg>') no-repeat center;
            background-size: contain;
        }

        .rotating {
            animation: rotate 8s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .moving-cloud {
            animation: moveCloud 6s ease-in-out infinite;
        }

        @keyframes moveCloud {
            0% { transform: translateX(0); }
            25% { transform: translateX(15px); }
            75% { transform: translateX(-15px); }
            100% { transform: translateX(0); }
        }

        .cat-active {
            animation: catPurr 2s ease-in-out infinite alternate;
        }

        @keyframes catPurr {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }
    </style>
</head>
<body>
    <div id="background"></div>

    <div class="season-overlay spring-overlay" id="spring-overlay"></div>
    <div class="season-overlay summer-overlay" id="summer-overlay"></div>
    <div class="season-overlay autumn-overlay" id="autumn-overlay"></div>
    <div class="season-overlay winter-overlay" id="winter-overlay"></div>

    <div id="particles-container"></div>

    <div class="interactive-object" id="windmill"></div>
    <div class="interactive-object" id="cat"></div>
    <div class="interactive-object" id="cloud"></div>

    <div id="cursor"></div>

    <button id="start-button">🎵 Spustit relaxaci</button>

    <script src="script.js"></script>
</body>
</html>
