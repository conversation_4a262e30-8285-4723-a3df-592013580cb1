# Rozcestník úkolů - <PERSON><PERSON>j

Tento rozcestník slouží jako centrální místo pro přehled všech úkolů praxe.

## Funkce

### ✅ Splněné požadavky:
- **Hezký design** - Moderní glassmorphism design s gradientním pozadím
- **Perfektní rozložení** - Responzivní grid layout pro všechny velikosti obrazovek
- **Funkční od<PERSON>** - Všechny odkazy se otevírají v novém okně
- **Identifikace autora** - Jméno a místo pro fotografii v hlavičce
- **Stav úkolů** - Vizuální indikace dokončených/nedokončených úkolů
- **Progress bar** - Ukazatel celkového pokroku

### 🎨 Design prvky:
- Glassmorphism efekt s průhledným pozadím
- <PERSON>rad<PERSON><PERSON><PERSON> barvy (modrá → fialová)
- Hover efekty a animace
- Font Awesome ikony
- Responzivní design pro mobily

### 📱 Responzivní design:
- Desktop: Grid 3-4 sloupce
- Tablet: Grid 2 sloupce  
- Mobil: Jeden sloupec

## Jak používat

### 1. Přidání profilové fotografie
Umístěte soubor `profile.jpg` do složky `rozcestnik/`. Fotografie se automaticky zobrazí místo placeholder ikony.

### 2. Automatická detekce úkolů
Rozcestník automaticky kontroluje existenci souborů `index.html` v jednotlivých složkách úkolů:
- ✅ Zelená ikona = úkol dokončen (soubor existuje)
- ❌ Oranžová ikona = úkol nedokončen (soubor neexistuje)

### 3. Struktura složek
```
praxe/
├── rozcestnik/
│   ├── index.html
│   ├── style.css
│   ├── script.js
│   └── profile.jpg (přidejte vlastní)
├── prx1/
│   └── index.html
├── prx2/
│   └── index.html
└── ...
```

### 4. Přizpůsobení
V souboru `index.html` můžete změnit:
- Jméno autora (řádek 21)
- Popisy úkolů (v každé `.task-preview`)
- Odkazy na úkoly (pokud máte jinou strukturu)

## Technické detaily

### Použité technologie:
- **HTML5** - Sémantická struktura
- **CSS3** - Moderní styling s flexbox/grid
- **JavaScript ES6+** - Dynamická kontrola úkolů
- **Font Awesome** - Ikony
- **Google Fonts** - Typography (Inter)

### Hlavní funkce JavaScriptu:
- `checkTasksStatus()` - Kontrola existence úkolů
- `checkProfilePhoto()` - Kontrola existence fotografie
- `updateProgress()` - Aktualizace progress baru

### CSS funkce:
- Glassmorphism efekt s `backdrop-filter`
- CSS Grid pro responzivní layout
- CSS animace a transitions
- Gradientní pozadí a prvky

## Testování

Pro testování můžete v konzoli prohlížeče použít:
```javascript
// Označit úkol jako dokončený
markTaskCompleted(1);

// Označit úkol jako nedokončený  
markTaskPending(1);

// Obnovit stav všech úkolů
refreshTasksStatus();
```

## Poznámky

- Rozcestník funguje i offline
- Automaticky se přizpůsobuje různým velikostem obrazovek
- Odkazy se otevírají v novém okně/tabu
- Vizuální feedback při hover efektech
- Progress bar se animuje při načtení stránky
