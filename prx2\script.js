let canvas = document.getElementById("canvas");
let boxId = 0;
let dragging = null;
let offsetX = 0, offsetY = 0;

function createBox(color) {
  let div = document.createElement("div");
  div.className = "box";
  div.style.background = color;
  div.style.width = 50 + Math.random() * 100 + "px";
  div.style.height = 50 + Math.random() * 100 + "px";
  div.style.left = Math.random() * (canvas.clientWidth - 100) + "px";
  div.style.top = Math.random() * (canvas.clientHeight - 100) + "px";
  div.dataset.id = boxId++;
  canvas.appendChild(div);

  div.addEventListener("mousedown", e => {
    dragging = div;
    offsetX = e.offsetX;
    offsetY = e.offsetY;
  });
}

document.addEventListener("mousemove", e => {
  if (dragging) {
    dragging.style.left = e.pageX - offsetX + "px";
    dragging.style.top = e.pageY - offsetY + "px";
  }
});

document.addEventListener("mouseup", () => {
  dragging = null;
});

function saveLayout() {
  let boxes = [...document.querySelectorAll(".box")].map(box => ({
    color: box.style.background,
    width: box.style.width,
    height: box.style.height,
    left: box.style.left,
    top: box.style.top
  }));

  let name = prompt("Zadejte název uložení:");
  if (!name) return;

  fetch("server.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ action: "save", name: name, data: boxes })
  }).then(() => updateSaveList());
}

function loadLayout(name) {
  if (!name) return;
  fetch(`server.php?action=load&name=${encodeURIComponent(name)}`)
    .then(res => res.json())
    .then(data => {
      canvas.innerHTML = "";
      boxId = 0;
      data.forEach(obj => {
        let div = document.createElement("div");
        div.className = "box";
        div.style.background = obj.color;
        div.style.width = obj.width;
        div.style.height = obj.height;
        div.style.left = obj.left;
        div.style.top = obj.top;
        div.dataset.id = boxId++;
        canvas.appendChild(div);

        div.addEventListener("mousedown", e => {
          dragging = div;
          offsetX = e.offsetX;
          offsetY = e.offsetY;
        });
      });
    });
}

function updateSaveList() {
  fetch("server.php?action=list")
    .then(res => res.json())
    .then(list => {
      const select = document.getElementById("loadList");
      select.innerHTML = `<option value="">Načíst uložený...</option>`;
      list.forEach(name => {
        let option = document.createElement("option");
        option.value = name;
        option.textContent = name;
        select.appendChild(option);
      });
    });
}

updateSaveList();