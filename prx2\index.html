<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8" />
  <title><PERSON>na kreslen<PERSON></title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div id="controls">
    <button onclick="createBox('red')"><PERSON>erven<PERSON> okno</button>
    <button onclick="createBox('green')"><PERSON><PERSON><PERSON><PERSON> okno</button>
    <button onclick="createBox('blue')">Modr<PERSON> okno</button>
    <button onclick="saveLayout()">💾 Uložit</button>
    <select id="loadList" onchange="loadLayout(this.value)">
      <option value="">Načíst uložený...</option>
    </select>
  </div>
  <div id="canvas"></div>
  <script src="script.js"></script>
</body>
</html>